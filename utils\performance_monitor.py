#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import numpy as np
import psutil
import time
from datetime import datetime
import logging
from typing import Dict, List, Any

class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'prediction_time': [],
            'training_time': [],
            'memory_usage': [],
            'cpu_usage': [],
            'command_execution_time': []
        }
        self.thresholds = {
            'memory_usage': 80,  # 80% 内存使用率
            'cpu_usage': 90,     # 90% CPU使用率
            'prediction_time': 5  # 5秒预测时间
        }
        self.alerts = []
        
    def record_metric(self, metric_name: str, value: float) -> None:
        """记录性能指标"""
        if metric_name in self.metrics:
            self.metrics[metric_name].append({
                'timestamp': datetime.now(),
                'value': value
            })
            self._check_thresholds(metric_name, value)
    
    def _check_thresholds(self, metric_name: str, value: float) -> None:
        """检查是否超过阈值"""
        if metric_name in self.thresholds and value > self.thresholds[metric_name]:
            self.alerts.append({
                'timestamp': datetime.now(),
                'metric': metric_name,
                'value': value,
                'threshold': self.thresholds[metric_name]
            })
            logging.warning(f"性能警告: {metric_name} 超过阈值 {self.thresholds[metric_name]}, 当前值: {value}")
    
    def get_statistics(self) -> Dict[str, Dict[str, float]]:
        """获取统计信息"""
        stats = {}
        for metric_name, values in self.metrics.items():
            if values:
                values_list = [v['value'] for v in values]
                stats[metric_name] = {
                    'mean': np.mean(values_list),
                    'max': np.max(values_list),
                    'min': np.min(values_list),
                    'std': np.std(values_list)
                }
        return stats
    
    def get_current_usage(self) -> Dict[str, float]:
        """获取当前系统资源使用情况"""
        return {
            'memory_usage': psutil.virtual_memory().percent,
            'cpu_usage': psutil.cpu_percent(),
            'disk_usage': psutil.disk_usage('/').percent
        }
    
    def clear_old_metrics(self, days: int = 7) -> None:
        """清理旧数据"""
        current_time = datetime.now()
        for metric_name in self.metrics:
            self.metrics[metric_name] = [
                v for v in self.metrics[metric_name]
                if (current_time - v['timestamp']).days < days
            ]
    
    def get_alerts(self, days: int = 1) -> List[Dict[str, Any]]:
        """获取指定天数内的告警"""
        current_time = datetime.now()
        return [
            alert for alert in self.alerts
            if (current_time - alert['timestamp']).days < days
        ] 