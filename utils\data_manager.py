#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import shutil
import logging
from datetime import datetime, timedelta
import pandas as pd
import json
from typing import Dict, List, Any, Optional
import gzip

class DataManager:
    def __init__(self, data_dir: str = 'data'):
        self.data_dir = data_dir
        self.backup_dir = os.path.join(data_dir, 'backups')
        self.ensure_directories()
        self.backup_schedule = {
            'daily': {'interval': 24, 'keep_days': 7},
            'weekly': {'interval': 168, 'keep_days': 30},
            'monthly': {'interval': 720, 'keep_days': 365}
        }
    
    def ensure_directories(self) -> None:
        """确保必要的目录存在"""
        try:
            os.makedirs(self.data_dir, exist_ok=True)
            os.makedirs(self.backup_dir, exist_ok=True)
        except Exception as e:
            logging.error(f"创建目录失败: {str(e)}")
    
    def backup_data(self, data_type: str, data: Any) -> bool:
        """备份数据"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = os.path.join(
                self.backup_dir,
                f'{data_type}_{timestamp}.gz'
            )
            
            # 根据数据类型选择序列化方法
            if isinstance(data, pd.DataFrame):
                data_str = data.to_json()
            elif isinstance(data, dict):
                data_str = json.dumps(data)
            else:
                data_str = str(data)
            
            # 压缩并保存
            with gzip.open(backup_file, 'wt') as f:
                f.write(data_str)
            
            logging.info(f"数据备份成功: {backup_file}")
            return True
        except Exception as e:
            logging.error(f"数据备份失败: {str(e)}")
            return False
    
    def restore_data(self, data_type: str, timestamp: str) -> Optional[Any]:
        """恢复数据"""
        try:
            backup_file = os.path.join(
                self.backup_dir,
                f'{data_type}_{timestamp}.gz'
            )
            
            if not os.path.exists(backup_file):
                logging.error(f"备份文件不存在: {backup_file}")
                return None
            
            # 读取并解压数据
            with gzip.open(backup_file, 'rt') as f:
                data_str = f.read()
            
            # 根据数据类型选择反序列化方法
            if data_type.endswith('_df'):
                return pd.read_json(data_str)
            elif data_type.endswith('_json'):
                return json.loads(data_str)
            else:
                return data_str
        except Exception as e:
            logging.error(f"数据恢复失败: {str(e)}")
            return None
    
    def cleanup_old_backups(self) -> None:
        """清理旧备份"""
        try:
            current_time = datetime.now()
            
            for backup_type, schedule in self.backup_schedule.items():
                cutoff_time = current_time - timedelta(days=schedule['keep_days'])
                
                # 获取所有备份文件
                backup_files = [
                    f for f in os.listdir(self.backup_dir)
                    if f.startswith(backup_type)
                ]
                
                # 删除过期备份
                for backup_file in backup_files:
                    file_path = os.path.join(self.backup_dir, backup_file)
                    file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                    
                    if file_time < cutoff_time:
                        os.remove(file_path)
                        logging.info(f"已删除过期备份: {backup_file}")
        except Exception as e:
            logging.error(f"清理旧备份失败: {str(e)}")
    
    def save_data(self, data_type: str, data: Any) -> bool:
        """保存数据"""
        try:
            file_path = os.path.join(self.data_dir, f'{data_type}.json')
            
            # 根据数据类型选择序列化方法
            if isinstance(data, pd.DataFrame):
                data.to_json(file_path)
            elif isinstance(data, dict):
                with open(file_path, 'w') as f:
                    json.dump(data, f, indent=4)
            else:
                with open(file_path, 'w') as f:
                    f.write(str(data))
            
            logging.info(f"数据保存成功: {file_path}")
            return True
        except Exception as e:
            logging.error(f"数据保存失败: {str(e)}")
            return False
    
    def load_data(self, data_type: str) -> Optional[Any]:
        """加载数据"""
        try:
            file_path = os.path.join(self.data_dir, f'{data_type}.json')
            
            if not os.path.exists(file_path):
                logging.warning(f"数据文件不存在: {file_path}")
                return None
            
            # 根据数据类型选择反序列化方法
            if data_type.endswith('_df'):
                return pd.read_json(file_path)
            elif data_type.endswith('_json'):
                with open(file_path, 'r') as f:
                    return json.load(f)
            else:
                with open(file_path, 'r') as f:
                    return f.read()
        except Exception as e:
            logging.error(f"数据加载失败: {str(e)}")
            return None
    
    def get_backup_list(self, data_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """获取备份列表"""
        try:
            backups = []
            for file_name in os.listdir(self.backup_dir):
                if data_type and not file_name.startswith(data_type):
                    continue
                
                file_path = os.path.join(self.backup_dir, file_name)
                file_stat = os.stat(file_path)
                
                backups.append({
                    'name': file_name,
                    'size': file_stat.st_size,
                    'created': datetime.fromtimestamp(file_stat.st_ctime),
                    'modified': datetime.fromtimestamp(file_stat.st_mtime)
                })
            
            return sorted(backups, key=lambda x: x['modified'], reverse=True)
        except Exception as e:
            logging.error(f"获取备份列表失败: {str(e)}")
            return []
    
    def verify_data_integrity(self, data_type: str) -> bool:
        """验证数据完整性"""
        try:
            file_path = os.path.join(self.data_dir, f'{data_type}.json')
            if not os.path.exists(file_path):
                return False
            
            # 尝试加载数据
            data = self.load_data(data_type)
            if data is None:
                return False
            
            # 根据数据类型进行特定验证
            if isinstance(data, pd.DataFrame):
                return not data.empty
            elif isinstance(data, dict):
                return bool(data)
            else:
                return bool(str(data).strip())
        except Exception as e:
            logging.error(f"数据完整性验证失败: {str(e)}")
            return False 