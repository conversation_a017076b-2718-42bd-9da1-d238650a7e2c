#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import logging
from typing import Dict, Any, Optional
from datetime import datetime

class ConfigManager:
    def __init__(self, config_file: str):
        self.config_file = config_file
        self.config = self.load_config()
        self.last_modified = os.path.getmtime(config_file)
        self.default_config = {
            'high_value_queues': ['AL_Ncsim'],
            'min_hosts_per_queue': {'AL_Ncsim': 3},
            'reserve_ratio': 0.2,
            'queue_priorities': {'AL_Ncsim': 10, 'AL_ADE-XL': 5},
            'cost_weight': 0.4,
            'performance_thresholds': {
                'memory_usage': 80,
                'cpu_usage': 90,
                'prediction_time': 5
            },
            'backup_settings': {
                'enabled': True,
                'interval_hours': 24,
                'keep_days': 7
            },
            'logging': {
                'level': 'INFO',
                'max_size_mb': 10,
                'backup_count': 5
            }
        }
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                return self._validate_config(config)
            else:
                logging.warning(f"配置文件 {self.config_file} 不存在，创建默认配置")
                return self.default_config
        except Exception as e:
            logging.error(f"加载配置文件失败: {str(e)}")
            return self.default_config
    
    def _validate_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """验证配置有效性"""
        validated_config = self.default_config.copy()
        
        # 更新配置，保留默认值
        for key, value in config.items():
            if key in self.default_config:
                if isinstance(value, type(self.default_config[key])):
                    validated_config[key] = value
                else:
                    logging.warning(f"配置项 {key} 类型不匹配，使用默认值")
        
        return validated_config
    
    def check_config_updates(self) -> bool:
        """检查配置是否更新"""
        try:
            current_modified = os.path.getmtime(self.config_file)
            if current_modified > self.last_modified:
                self.config = self.load_config()
                self.last_modified = current_modified
                logging.info("配置已更新")
                return True
            return False
        except Exception as e:
            logging.error(f"检查配置更新失败: {str(e)}")
            return False
    
    def save_config(self) -> bool:
        """保存配置到文件"""
        try:
            with open(self.config_file, 'w') as f:
                json.dump(self.config, f, indent=4)
            self.last_modified = os.path.getmtime(self.config_file)
            logging.info("配置已保存")
            return True
        except Exception as e:
            logging.error(f"保存配置失败: {str(e)}")
            return False
    
    def get_value(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self.config.get(key, default)
    
    def set_value(self, key: str, value: Any) -> bool:
        """设置配置值"""
        try:
            if key in self.default_config:
                if isinstance(value, type(self.default_config[key])):
                    self.config[key] = value
                    return self.save_config()
                else:
                    logging.error(f"配置值类型不匹配: {key}")
                    return False
            else:
                logging.error(f"未知的配置项: {key}")
                return False
        except Exception as e:
            logging.error(f"设置配置值失败: {str(e)}")
            return False
    
    def reset_to_default(self) -> bool:
        """重置为默认配置"""
        try:
            self.config = self.default_config.copy()
            return self.save_config()
        except Exception as e:
            logging.error(f"重置配置失败: {str(e)}")
            return False 