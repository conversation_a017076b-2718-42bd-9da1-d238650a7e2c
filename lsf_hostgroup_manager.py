#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import argparse
import os
import sys
import re
import subprocess
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('lsf_hostgroup_manager.log'),
        logging.StreamHandler()
    ]
)

# LSF配置文件路径
LSF_CONFIG_DIR = '/common/lsftool/lsf/mnt/conf/lsbatch/rdb/configdir'
LSF_HOSTS_FILE = os.path.join(LSF_CONFIG_DIR, 'lsb.hosts')

def find_hostgroup_in_config(config_content, group_name):
    """在配置文件内容中查找主机组定义"""
    # 查找主机组定义的正则表达式
    pattern = rf'Begin\s+HostGroup\s+{re.escape(group_name)}\s+(.*?)\s+End\s+HostGroup'
    match = re.search(pattern, config_content, re.DOTALL)
    
    if not match:
        # 尝试查找简单格式的主机组定义
        pattern = rf'{re.escape(group_name)}\s*=\s*(.*?)(?:\n|$)'
        match = re.search(pattern, config_content)
        
    return match

def modify_hostgroup(group_name, host_name, action):
    """修改主机组配置
    
    Args:
        group_name: 主机组名称
        host_name: 主机名
        action: 'add' 或 'remove'
    
    Returns:
        bool: 操作是否成功
    """
    try:
        # 检查配置文件是否存在
        if not os.path.exists(LSF_HOSTS_FILE):
            logging.error(f"LSF主机组配置文件不存在: {LSF_HOSTS_FILE}")
            return False
        
        # 读取配置文件
        with open(LSF_HOSTS_FILE, 'r') as f:
            content = f.read()
        
        # 查找主机组定义
        match = find_hostgroup_in_config(content, group_name)
        if not match:
            logging.error(f"在配置文件中未找到主机组 {group_name}")
            return False
        
        # 提取主机组定义
        if len(match.groups()) > 0:
            hosts_str = match.group(1)
            # 清理字符串，移除注释和多余空白
            hosts_str = re.sub(r'#.*$', '', hosts_str, flags=re.MULTILINE)
            hosts = [h.strip() for h in re.split(r'\s+', hosts_str) if h.strip()]
        else:
            logging.error(f"无法解析主机组 {group_name} 的定义")
            return False
        
        # 根据操作修改主机列表
        if action == 'add':
            if host_name not in hosts:
                hosts.append(host_name)
                logging.info(f"将主机 {host_name} 添加到主机组 {group_name}")
            else:
                logging.info(f"主机 {host_name} 已经在主机组 {group_name} 中")
                return True
        elif action == 'remove':
            if host_name in hosts:
                hosts.remove(host_name)
                logging.info(f"从主机组 {group_name} 中移除主机 {host_name}")
            else:
                logging.info(f"主机 {host_name} 不在主机组 {group_name} 中")
                return True
        
        # 构建新的主机组定义
        new_hosts_str = ' '.join(hosts)
        
        # 更新配置文件内容
        if len(match.groups()) > 0:
            # 复杂格式的主机组定义
            new_content = content[:match.start(1)] + new_hosts_str + content[match.end(1):]
        else:
            # 简单格式的主机组定义
            new_content = re.sub(
                rf'{re.escape(group_name)}\s*=\s*(.*?)(?:\n|$)',
                f'{group_name} = {new_hosts_str}\n',
                content
            )
        
        # 备份原配置文件
        backup_file = f"{LSF_HOSTS_FILE}.bak"
        with open(backup_file, 'w') as f:
            f.write(content)
        logging.info(f"已备份原配置文件到 {backup_file}")
        
        # 写入新配置
        with open(LSF_HOSTS_FILE, 'w') as f:
            f.write(new_content)
        logging.info(f"已更新主机组配置文件")
        
        # 重新配置LSF
        logging.info("执行 LSF 重新配置")
        result = subprocess.run('badmin reconfig', shell=True, capture_output=True, text=True)
        
        if result.returncode != 0:
            logging.error(f"LSF 重新配置失败: {result.stderr}")
            # 恢复备份
            with open(LSF_HOSTS_FILE, 'w') as f:
                f.write(content)
            logging.info("已恢复原配置文件")
            return False
        
        logging.info("LSF 重新配置成功，主机组变更已生效")
        return True
        
    except Exception as e:
        logging.error(f"修改主机组时出错: {str(e)}")
        return False

def main():
    parser = argparse.ArgumentParser(description='LSF主机组管理工具')
    subparsers = parser.add_subparsers(dest='action', help='操作类型')
    
    # 添加主机到主机组
    add_parser = subparsers.add_parser('add', help='添加主机到主机组')
    add_parser.add_argument('-g', '--group', required=True, help='主机组名称')
    add_parser.add_argument('-h', '--host', required=True, help='主机名')
    
    # 从主机组移除主机
    remove_parser = subparsers.add_parser('remove', help='从主机组移除主机')
    remove_parser.add_argument('-g', '--group', required=True, help='主机组名称')
    remove_parser.add_argument('-h', '--host', required=True, help='主机名')
    
    # 列出主机组成员
    list_parser = subparsers.add_parser('list', help='列出主机组成员')
    list_parser.add_argument('-g', '--group', required=True, help='主机组名称')
    
    args = parser.parse_args()
    
    if args.action == 'add':
        success = modify_hostgroup(args.group, args.host, 'add')
        sys.exit(0 if success else 1)
    elif args.action == 'remove':
        success = modify_hostgroup(args.group, args.host, 'remove')
        sys.exit(0 if success else 1)
    elif args.action == 'list':
        # 读取配置文件
        try:
            with open(LSF_HOSTS_FILE, 'r') as f:
                content = f.read()
            
            # 查找主机组定义
            match = find_hostgroup_in_config(content, args.group)
            if not match:
                logging.error(f"在配置文件中未找到主机组 {args.group}")
                sys.exit(1)
            
            # 提取主机组定义
            if len(match.groups()) > 0:
                hosts_str = match.group(1)
                # 清理字符串，移除注释和多余空白
                hosts_str = re.sub(r'#.*$', '', hosts_str, flags=re.MULTILINE)
                hosts = [h.strip() for h in re.split(r'\s+', hosts_str) if h.strip()]
                print(f"主机组 {args.group} 的成员:")
                for host in hosts:
                    print(f"  {host}")
            else:
                logging.error(f"无法解析主机组 {args.group} 的定义")
                sys.exit(1)
        except Exception as e:
            logging.error(f"列出主机组成员时出错: {str(e)}")
            sys.exit(1)
    else:
        parser.print_help()
        sys.exit(1)

if __name__ == "__main__":
    main()