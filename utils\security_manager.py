#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re
import logging
from typing import Set, Dict, List, Optional
import hashlib
import os
from datetime import datetime, timedelta

class SecurityManager:
    def __init__(self):
        self.allowed_commands = {
            'lsload', 'bqueues', 'badmin', 'lshosts'
        }
        self.user_permissions = {}
        self.command_history = []
        self.max_history_size = 1000
        self.sensitive_patterns = [
            r'password\s*=\s*[\'"][^\'"]+[\'"]',
            r'api_key\s*=\s*[\'"][^\'"]+[\'"]',
            r'token\s*=\s*[\'"][^\'"]+[\'"]'
        ]
    
    def validate_command(self, command: str) -> bool:
        """验证命令是否允许执行"""
        try:
            # 提取基本命令
            base_cmd = command.split()[0] if command else ''
            return base_cmd in self.allowed_commands
        except Exception as e:
            logging.error(f"命令验证失败: {str(e)}")
            return False
    
    def sanitize_input(self, input_str: str) -> str:
        """净化输入字符串"""
        try:
            # 移除危险字符
            sanitized = re.sub(r'[;&|`$]', '', input_str)
            # 移除敏感信息
            for pattern in self.sensitive_patterns:
                sanitized = re.sub(pattern, '[REDACTED]', sanitized)
            return sanitized
        except Exception as e:
            logging.error(f"输入净化失败: {str(e)}")
            return ''
    
    def record_command(self, command: str, user: str) -> None:
        """记录命令执行历史"""
        try:
            self.command_history.append({
                'timestamp': datetime.now(),
                'command': self.sanitize_input(command),
                'user': user
            })
            # 保持历史记录在限制大小内
            if len(self.command_history) > self.max_history_size:
                self.command_history = self.command_history[-self.max_history_size:]
        except Exception as e:
            logging.error(f"记录命令失败: {str(e)}")
    
    def get_command_history(self, 
                          days: int = 1, 
                          user: Optional[str] = None) -> List[Dict]:
        """获取命令执行历史"""
        try:
            cutoff_time = datetime.now() - timedelta(days=days)
            history = [
                record for record in self.command_history
                if record['timestamp'] > cutoff_time
            ]
            if user:
                history = [record for record in history if record['user'] == user]
            return history
        except Exception as e:
            logging.error(f"获取命令历史失败: {str(e)}")
            return []
    
    def add_allowed_command(self, command: str) -> bool:
        """添加允许的命令"""
        try:
            if command and command.strip():
                self.allowed_commands.add(command.strip())
                return True
            return False
        except Exception as e:
            logging.error(f"添加允许命令失败: {str(e)}")
            return False
    
    def remove_allowed_command(self, command: str) -> bool:
        """移除允许的命令"""
        try:
            if command in self.allowed_commands:
                self.allowed_commands.remove(command)
                return True
            return False
        except Exception as e:
            logging.error(f"移除允许命令失败: {str(e)}")
            return False
    
    def set_user_permission(self, user: str, permissions: Set[str]) -> bool:
        """设置用户权限"""
        try:
            if user and permissions:
                self.user_permissions[user] = permissions
                return True
            return False
        except Exception as e:
            logging.error(f"设置用户权限失败: {str(e)}")
            return False
    
    def check_user_permission(self, user: str, permission: str) -> bool:
        """检查用户权限"""
        try:
            return user in self.user_permissions and permission in self.user_permissions[user]
        except Exception as e:
            logging.error(f"检查用户权限失败: {str(e)}")
            return False
    
    def hash_sensitive_data(self, data: str) -> str:
        """对敏感数据进行哈希处理"""
        try:
            return hashlib.sha256(data.encode()).hexdigest()
        except Exception as e:
            logging.error(f"数据哈希处理失败: {str(e)}")
            return ''
    
    def check_file_permissions(self, file_path: str) -> bool:
        """检查文件权限"""
        try:
            if not os.path.exists(file_path):
                return False
            # 检查文件权限
            stat = os.stat(file_path)
            # 确保文件权限安全
            return stat.st_mode & 0o777 == 0o644
        except Exception as e:
            logging.error(f"检查文件权限失败: {str(e)}")
            return False 