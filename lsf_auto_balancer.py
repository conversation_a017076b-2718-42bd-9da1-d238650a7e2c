#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import subprocess
import json
import time
import logging
from logging.handlers import RotatingFileHandler
import pandas as pd
from datetime import datetime, timedelta
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import cross_val_score
import joblib
import os
import re
import math
import sys
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        RotatingFileHandler('lsf_balancer.log', maxBytes=10*1024*1024, backupCount=5),  # 10MB, 保留5个备份
        logging.StreamHandler()
    ]
)

def check_dependencies():
    """检查必要的依赖是否已安装"""
    missing_deps = []
    try:
        import pandas
    except ImportError:
        missing_deps.append("pandas")
    try:
        import numpy
    except ImportError:
        missing_deps.append("numpy")
    try:
        import sklearn
    except ImportError:
        missing_deps.append("scikit-learn")
    try:
        import joblib
    except ImportError:
        missing_deps.append("joblib")

    if missing_deps:
        print(f"错误: 缺少以下依赖: {', '.join(missing_deps)}")
        print("请使用pip安装这些依赖: pip install " + " ".join(missing_deps))
        return False
    return True

def check_lsf_environment():
    """检查LSF环境是否正确配置"""
    try:
        result = subprocess.run("which lsload", shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            logging.error("LSF命令'lsload'不可用，请确保LSF环境已正确配置")
            return False

        result = subprocess.run("which bqueues", shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            logging.error("LSF命令'bqueues'不可用，请确保LSF环境已正确配置")
            return False

        return True
    except Exception as e:
        logging.error(f"检查LSF环境时出错: {str(e)}")

        return False

class LSFBalancer:
    def __init__(self):
        """初始化LSF平衡器"""
        self.host_groups = {
            'HOSTGR_S_RHEL7_Ncsim': ['bcsl145', 'bcsl146'],
            'HOSTGR_S_OnlyADE_Port_cfg': ['bcsl143', 'bcsl144']
        }
        self.queues = {
            'HOSTGR_S_RHEL7_Ncsim': 'AL_Ncsim',
            'HOSTGR_S_OnlyADE_Port_cfg': 'AL_ADE-XL'
        }
        self.history_file = 'lsf_history.csv'
        self.model_file = 'lsf_model.joblib'
        self.cost_file = 'lsf_cost.csv'
        self.transfer_history_file = 'lsf_transfer_history.csv'
        self.price_file = '/local_work/lsfmon_v1.9.0/bin/Queue_Price'
        self.config_file = 'lsf_balancer_config.json'

        # 添加成本权重配置
        self.cost_weight = 0.4  # 成本因素的权重(0-1)，越高表示越重视成本节省
        self.performance_weight = 0.6  # 性能因素的权重(0-1)，越高表示越重视性能提升

        # 确保权重总和为1
        total_weight = self.cost_weight + self.performance_weight
        self.cost_weight /= total_weight
        self.performance_weight /= total_weight

        # 加载配置文件
        self.load_config()

        self.queue_prices = self.load_queue_prices()
        self.load_history()
        self.load_cost_data()
        self.load_transfer_history()
        self.model = self.load_or_create_model()
        self.last_price_load_time = datetime.now()

        # 添加作业预测相关配置
        self.job_history_file = 'lsf_job_history.csv'
        self.job_forecast_horizon = 24  # 预测未来24小时的作业量
        self.high_value_queues = ['AL_Ncsim']  # 高价值队列列表
        self.min_hosts_per_queue = {'AL_Ncsim': 3}  # 每个队列的最小主机数

        # 资源预留策略配置
        self.reserve_ratio = 0.2  # 为突发作业预留的资源比例
        self.queue_priorities = {'AL_Ncsim': 10, 'AL_ADE-XL': 5}
        self.host_capabilities = {}  # 主机性能和特性

        # 加载作业历史数据
        self.load_job_history()

        # 加载主机性能数据
        self.load_host_capabilities()

        # 初始化作业预测模型
        self.job_forecast_model = self.initialize_job_forecast_model()
        
        # 初始化队列限制优化器
        self.limit_optimizer = QueueLimitOptimizer()

    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r') as f:
                    config = json.load(f)

                # 更新配置
                if 'high_value_queues' in config:
                    self.high_value_queues = config['high_value_queues']
                if 'min_hosts_per_queue' in config:
                    self.min_hosts_per_queue = config['min_hosts_per_queue']
                if 'reserve_ratio' in config:
                    self.reserve_ratio = config['reserve_ratio']
                if 'queue_priorities' in config:
                    self.queue_priorities = config['queue_priorities']
                if 'cost_weight' in config:
                    self.cost_weight = config['cost_weight']
                    self.performance_weight = 1 - self.cost_weight

                # 加载动态限制配置
                if 'dynamic_limits' in config:
                    self.dynamic_limits = config['dynamic_limits']
                else:
                    # 设置默认的动态限制配置
                    self.dynamic_limits = {
                        "enabled": True,
                        "max_adjustment": {
                            "increase_threshold": 0.9,
                            "decrease_threshold": 0.5,
                            "increase_factor": 1.2,
                            "decrease_factor": 0.9
                        },
                        "jlu_adjustment": {
                            "increase_threshold": 0.2,
                            "decrease_threshold": 0.8,
                            "increase_factor": 1.1,
                            "decrease_factor": 0.9
                        },
                        "min_max": {},
                        "max_max": {},
                        "min_jlu": {},
                        "max_jlu": {}
                    }

                logging.info(f"已从 {self.config_file} 加载配置")
            else:
                # 创建默认配置文件
                self.save_config()
        except Exception as e:
            logging.error(f"加载配置文件失败: {str(e)}")

    def save_config(self):
        """保存配置到文件"""
        try:
            config = {
                'high_value_queues': self.high_value_queues,
                'min_hosts_per_queue': self.min_hosts_per_queue,
                'reserve_ratio': self.reserve_ratio,
                'queue_priorities': self.queue_priorities,
                'cost_weight': self.cost_weight,
                'dynamic_limits': self.dynamic_limits if hasattr(self, 'dynamic_limits') else {
                    "enabled": True,
                    "max_adjustment": {
                        "increase_threshold": 0.9,
                        "decrease_threshold": 0.5,
                        "increase_factor": 1.2,
                        "decrease_factor": 0.9
                    },
                    "jlu_adjustment": {
                        "increase_threshold": 0.2,
                        "decrease_threshold": 0.8,
                        "increase_factor": 1.1,
                        "decrease_factor": 0.9
                    },
                    "min_max": {},
                    "max_max": {},
                    "min_jlu": {},
                    "max_jlu": {}
                }
            }

            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=4)

            logging.info(f"已保存配置到 {self.config_file}")
        except Exception as e:
            logging.error(f"保存配置文件失败: {str(e)}")

    def load_host_capabilities(self):
        """加载主机性能和特性数据"""
        try:
            # 获取所有主机
            all_hosts = []
            for hosts in self.host_groups.values():
                all_hosts.extend(hosts)

            # 去重
            all_hosts = list(set(all_hosts))

            for host in all_hosts:
                # 获取主机信息
                cmd = f"lshosts -l {host}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

                if result.returncode != 0:
                    logging.error(f"获取主机 {host} 信息失败: {result.stderr}")
                    continue

                output = result.stdout

                # 解析CPU数量
                cpu_match = re.search(r'ncpus\s+(\d+)', output)
                cpus = int(cpu_match.group(1)) if cpu_match else 1

                # 解析内存大小
                mem_match = re.search(r'maxmem\s+(\d+)([KMG])', output)
                if mem_match:
                    mem_size = float(mem_match.group(1))
                    unit = mem_match.group(2)
                    if unit == 'K':
                        mem_size /= 1024  # 转换为MB
                    elif unit == 'G':
                        mem_size *= 1024  # 转换为MB
                else:
                    mem_size = 1024  # 默认1GB

                # 解析CPU型号和性能
                model_match = re.search(r'MODEL\s+(.+?)[\r\n]', output)
                model = model_match.group(1).strip() if model_match else "Unknown"

                # 计算性能分数 (简单估算)
                performance_score = cpus * (mem_size / 1024)  # CPU数 * 内存GB数

                # 存储主机性能数据
                self.host_capabilities[host] = {
                    'cpus': cpus,
                    'memory': mem_size,
                    'model': model,
                    'performance_score': performance_score
                }

                logging.info(f"主机 {host} 性能: CPU {cpus}核, 内存 {mem_size:.0f}MB, 型号 {model}, 性能分数 {performance_score:.1f}")

        except Exception as e:
            logging.error(f"加载主机性能数据失败: {str(e)}")

    def set_cost_weight(self, cost_weight):
        """设置成本权重"""
        self.cost_weight = max(0, min(cost_weight, 1))  # 确保权重在0-1之间
        self.performance_weight = 1 - self.cost_weight

        # 确保权重总和为1
        total_weight = self.cost_weight + self.performance_weight
        self.cost_weight /= total_weight
        self.performance_weight /= total_weight

        logging.info(f"成本权重已设置为: {self.cost_weight:.2f}, 性能权重: {self.performance_weight:.2f}")

    def load_queue_prices(self):
        """加载队列单价数据（人民币/元）"""
        queue_prices = {}
        try:
            if os.path.exists(self.price_file):
                with open(self.price_file, 'r') as f:
                    for line in f:
                        parts = line.strip().split()
                        if len(parts) >= 2:
                            queue_name = parts[0]
                            price = float(parts[1])
                            queue_prices[queue_name] = price
                            logging.info(f"加载队列 {queue_name} 的单价: {price} 元/小时")
            else:
                logging.warning(f"单价文件 {self.price_file} 不存在，使用默认单价")
                # 设置默认单价（人民币/元）
                queue_prices = {
                    'AL_ADE-XL': 5.47,
                    'AL_Ncsim': 7.61
                }
        except Exception as e:
            logging.error(f"加载队列单价失败: {str(e)}")
            # 设置默认单价（人民币/元）
            queue_prices = {
                'AL_ADE-XL': 5.47,
                'AL_Ncsim': 7.61
            }
        return queue_prices

    def parse_host_load(self, host):
        """解析主机负载信息"""
        try:
            cmd = f"lsload -l {host}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode != 0:
                logging.error(f"获取主机 {host} 负载信息失败: {result.stderr}")
                return {
                    'cpu_usage': 0.0,
                    'memory_usage': 0.0,
                    'load': 0.0
                }

            output = result.stdout

            # 解析CPU使用率
            cpu_match = re.search(r'ut=([0-9.]+)%', output)
            cpu_usage = float(cpu_match.group(1)) / 100.0 if cpu_match else 0.0

            # 解析内存使用率
            mem_match = re.search(r'mem=([0-9.]+)%', output)
            memory_usage = float(mem_match.group(1)) / 100.0 if mem_match else 0.0

            # 解析负载
            load_match = re.search(r'r15s=([0-9.]+)', output)
            load = float(load_match.group(1)) if load_match else 0.0

            host_info = {
                'cpu_usage': cpu_usage,
                'memory_usage': memory_usage,
                'load': load
            }

            # 如果没有找到CPU使用率，可以尝试从负载信息估算
            if host_info['cpu_usage'] == 0.0 and host_info['load'] > 0.0:
                # 假设负载1.0对应100%CPU使用率
                host_info['cpu_usage'] = min(1.0, host_info['load'])

            logging.debug(f"解析主机负载信息: {host_info}")
            return host_info
        except Exception as e:
            logging.error(f"解析主机负载信息失败: {str(e)}")
            return {
                'cpu_usage': 0.0,
                'memory_usage': 0.0,
                'load': 0.0
            }

    def load_history(self):
        """加载历史数据"""
        if os.path.exists(self.history_file):
            self.history = pd.read_csv(self.history_file)
        else:
            self.history = pd.DataFrame(columns=[
                'timestamp', 'queue', 'host', 'cpu_usage', 'memory_usage',
                'running_jobs', 'pending_jobs', 'transfer_amount', 'cost_saved'
            ])

    def load_cost_data(self):
        """加载成本数据"""
        if os.path.exists(self.cost_file):
            self.cost_data = pd.read_csv(self.cost_file)
        else:
            # 创建成本数据框架，记录每个主机组和队列的使用成本
            self.cost_data = pd.DataFrame(columns=[
                'week', 'host_group', 'queue', 'cost', 'jobs_completed'
            ])
            # 初始化默认成本数据
            default_data = []
            current_week = datetime.now().isocalendar()[1]  # 获取当前周数
            for group in self.host_groups:
                default_data.append({
                    'week': current_week,
                    'host_group': group,
                    'queue': self.queues[group],
                    'cost': 0.0,
                    'jobs_completed': 0
                })
            self.cost_data = pd.DataFrame(default_data)
            self.cost_data.to_csv(self.cost_file, index=False)

    def load_transfer_history(self):
        """加载主机组转移历史记录"""
        if os.path.exists(self.transfer_history_file):
            self.transfer_history = pd.read_csv(self.transfer_history_file)
        else:
            # 创建转移历史数据框架
            self.transfer_history = pd.DataFrame(columns=[
                'timestamp', 'host', 'source_group', 'target_group', 'reason', 'cost_saved'
            ])
            # 初始化空的转移历史记录
            self.transfer_history.to_csv(self.transfer_history_file, index=False)

    def update_cost_data(self, host_group, queue, cost_increment, jobs_completed=1):
        """更新成本数据"""
        current_week = datetime.now().isocalendar()[1]  # 获取当前周数

        # 检查是否存在当前周的记录
        mask = (self.cost_data['week'] == current_week) & \
               (self.cost_data['host_group'] == host_group) & \
               (self.cost_data['queue'] == queue)

        if mask.any():
            # 更新现有记录
            self.cost_data.loc[mask, 'cost'] += cost_increment
            self.cost_data.loc[mask, 'jobs_completed'] += jobs_completed
        else:
            # 添加新记录
            new_record = {
                'week': current_week,
                'host_group': host_group,
                'queue': queue,
                'cost': cost_increment,
                'jobs_completed': jobs_completed
            }
            self.cost_data = pd.concat([self.cost_data, pd.DataFrame([new_record])], ignore_index=True)

        # 保存更新后的成本数据
        self.cost_data.to_csv(self.cost_file, index=False)

    def save_history(self):
        """保存历史数据"""
        self.history.to_csv(self.history_file, index=False)

    def load_or_create_model(self):
        """加载或创建机器学习模型"""
        if os.path.exists(self.model_file):
            return joblib.load(self.model_file)
        return RandomForestRegressor(n_estimators=100)

    def train_model(self):
        """训练模型"""
        if len(self.history) < 10:  # 需要足够的数据才能训练
            logging.info("历史数据不足，暂不训练模型")
            return False

        try:
            # 增加特征工程
            self.history['load_ratio'] = self.history['cpu_usage'] / (self.history['memory_usage'] + 0.1)
            self.history['job_ratio'] = self.history['running_jobs'] / (self.history['pending_jobs'] + 1)

            # 使用更多特征进行训练
            X = self.history[[
                'cpu_usage', 'memory_usage', 'running_jobs', 'pending_jobs',
                'load_ratio', 'job_ratio'
            ]]

            # 如果有成本数据，将其作为目标变量
            if 'cost_saved' in self.history.columns and self.history['cost_saved'].notna().any():
                y = self.history['cost_saved']
                logging.info("使用成本节省数据作为训练目标")
            else:
                y = self.history['transfer_amount']
                logging.info("使用资源转移量作为训练目标")

            # 增加模型复杂度
            self.model = RandomForestRegressor(
                n_estimators=200,
                max_depth=10,
                min_samples_split=5,
                random_state=42
            )

            # 训练模型
            self.model.fit(X, y)

            # 评估模型性能
            predictions = self.model.predict(X)
            mse = np.mean((predictions - y) ** 2)
            logging.info(f"模型训练完成，MSE: {mse:.4f}")

            # 保存模型
            joblib.dump(self.model, self.model_file)
            return True
        except Exception as e:
            logging.error(f"模型训练失败: {str(e)}")
            return False

    def get_queue_load(self, queue_name):
        """获取队列负载信息，包括 MAX 和 JL/U 参数"""
        try:
            # 使用bqueues命令获取队列信息
            cmd = f"bqueues -l {queue_name}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode != 0:
                logging.error(f"获取队列 {queue_name} 信息失败: {result.stderr}")
                return None

            output = result.stdout

            # 解析 MAX 参数
            max_match = re.search(r'MAX\s+(\d+)', output)
            max_jobs = int(max_match.group(1)) if max_match else 0

            # 解析 JL/U 参数
            jlu_match = re.search(r'JL/U\s+(\d+)', output)
            max_jobs_per_user = int(jlu_match.group(1)) if jlu_match else 0

            # 解析运行中的作业数
            running_match = re.search(r'RUN\s+(\d+)', output)
            running_jobs = int(running_match.group(1)) if running_match else 0

            # 解析等待中的作业数
            pending_match = re.search(r'PEND\s+(\d+)', output)
            pending_jobs = int(pending_match.group(1)) if pending_match else 0

            # 解析 PJOBS
            pjobs_match = re.search(r'PJOBS\s+(\d+)', output)
            pjobs = int(pjobs_match.group(1)) if pjobs_match else 0

            # 获取队列中所有主机的平均CPU和内存使用率
            hosts = []
            hosts_match = re.search(r'HOSTS:\s+(.+?)[\r\n]', output)
            if hosts_match:
                hosts_str = hosts_match.group(1)
                hosts = [h.strip() for h in hosts_str.split() if h.strip()]

            total_cpu = 0.0
            total_mem = 0.0
            host_count = 0

            for host in hosts:
                host_load = self.parse_host_load(host)
                if host_load:
                    total_cpu += host_load['cpu_usage']
                    total_mem += host_load['memory_usage']
                    host_count += 1

            avg_cpu = total_cpu / max(1, host_count)
            avg_mem = total_mem / max(1, host_count)

            return {
                'max_jobs': max_jobs,
                'max_jobs_per_user': max_jobs_per_user,
                'running_jobs': running_jobs,
                'pending_jobs': pending_jobs,
                'pjobs': pjobs,
                'cpu_usage': avg_cpu,
                'memory_usage': avg_mem
            }
        except Exception as e:
            logging.error(f"获取队列负载信息失败: {str(e)}")
            return None

    def predict_transfer_amount(self, current_state, queue_name):
        """预测需要转移的资源量，考虑成本权重"""
        if len(self.history) < 10:
            # 数据不足时使用基于规则的预测
            return self._rule_based_prediction(current_state, queue_name)

        try:
            # 使用机器学习模型预测基础转移量
            base_prediction = self.model.predict([current_state])[0]

            # 获取队列单价
            queue_price = self.queue_prices.get(queue_name, 5.0)

            # 根据单价调整转移量
            # 单价越高，转移决策越谨慎（如果成本权重高）
            price_factor = 1.0
            if self.cost_weight > 0.3:  # 只有当成本权重足够高时才考虑价格因素
                # 计算相对价格因子（相对于平均价格）
                avg_price = sum(self.queue_prices.values()) / max(1, len(self.queue_prices))
                price_ratio = queue_price / avg_price

                # 价格高于平均值时减少转移量，低于平均值时增加转移量
                price_factor = 1.0 / (price_ratio ** self.cost_weight)

            # 调整预测值
            adjusted_prediction = base_prediction * price_factor

            # 确保预测值在0-1之间
            adjusted_prediction = max(0, min(adjusted_prediction, 1))

            # 记录预测结果
            logging.info(f"队列 {queue_name} (单价: {queue_price:.2f}元) 的资源转移预测:")
            logging.info(f"  基础预测: {base_prediction:.4f}")
            logging.info(f"  价格因子: {price_factor:.4f}")
            logging.info(f"  调整后预测: {adjusted_prediction:.4f}")

            return adjusted_prediction
        except Exception as e:
            logging.error(f"模型预测失败: {str(e)}")
            # 出错时回退到基于规则的预测
            return self._rule_based_prediction(current_state, queue_name)

    def _rule_based_prediction(self, current_state, queue_name):
        """基于规则的预测方法，作为机器学习模型的备选，考虑成本因素"""
        # 解析当前状态
        cpu_usage = current_state[0]
        memory_usage = current_state[1]
        running_jobs = current_state[2]
        pending_jobs = current_state[3]

        # 获取队列单价
        queue_price = self.queue_prices.get(queue_name, 5.0)

        # 计算基础转移量
        base_transfer = 0.0
        if cpu_usage > 0.8 and pending_jobs > 0:
            base_transfer = 0.7  # 高负载情况
        elif cpu_usage > 0.6 and pending_jobs > 3:
            base_transfer = 0.5  # 中等负载情况
        elif cpu_usage > 0.4 and pending_jobs > 5:
            base_transfer = 0.3  # 轻度负载情况
        else:
            base_transfer = 0.1  # 低负载情况

        # 根据单价和成本权重调整转移量
        avg_price = sum(self.queue_prices.values()) / max(1, len(self.queue_prices))
        price_ratio = queue_price / avg_price

        # 价格高于平均值时减少转移量，低于平均值时增加转移量
        price_factor = 1.0 / (price_ratio ** self.cost_weight)
        adjusted_transfer = base_transfer * price_factor

        # 确保转移量在0-1之间
        adjusted_transfer = max(0, min(adjusted_transfer, 1))

        logging.info(f"基于规则的预测 - 队列 {queue_name} (单价: {queue_price:.2f}元):")
        logging.info(f"  基础转移量: {base_transfer:.4f}")
        logging.info(f"  价格因子: {price_factor:.4f}")
        logging.info(f"  调整后转移量: {adjusted_transfer:.4f}")

        return adjusted_transfer

    def balance_resources(self):
        """平衡资源分配，考虑高价值队列和未来作业预测"""
        try:
            # 定期重新加载单价文件
            current_time = datetime.now()
            if not hasattr(self, 'last_price_load_time') or \
               (current_time - self.last_price_load_time).seconds >= 3600:
                self.queue_prices = self.load_queue_prices()
                self.last_price_load_time = current_time

            # 收集当前作业数据
            self.collect_job_data()

            changes_made = False

            # 分析每周成本数据
            if hasattr(self, 'analyze_weekly_cost'):
                self.analyze_weekly_cost()

            # 预测未来作业量
            future_jobs_by_queue = {}
            for queue in self.queues.values():
                future_jobs = self.predict_future_jobs(queue)
                if future_jobs:
                    # 计算未来24小时的总预期作业量
                    total_future_jobs = sum(future_jobs)
                    future_jobs_by_queue[queue] = total_future_jobs
                    logging.info(f"队列 {queue} 预计未来24小时将有 {total_future_jobs:.1f} 个作业提交")

            # 计算每个队列所需的主机数量
            required_hosts_by_queue = self.calculate_required_hosts(future_jobs_by_queue)

            # 检查高价值队列是否有足够的主机
            for queue, hosts_required in required_hosts_by_queue.items():
                if queue in self.high_value_queues:
                    # 找到对应的主机组
                    host_group = None
                    for group, q in self.queues.items():
                        if q == queue:
                            host_group = group
                            break

                    if not host_group:
                        continue

                    current_hosts = len(self.host_groups.get(host_group, []))

                    if current_hosts < hosts_required:
                        logging.warning(f"高价值队列 {queue} 主机不足: 当前 {current_hosts}, 需要 {hosts_required}")

                        # 尝试从其他组转移主机到高价值队列
                        hosts_needed = hosts_required - current_hosts
                        self.transfer_hosts_to_high_value_queue(host_group, queue, hosts_needed)
                        changes_made = True

            # 继续常规的资源平衡
            for group, hosts in self.host_groups.items():
                queue = self.queues[group]
                queue_load = self.get_queue_load(queue)

                if not queue_load:
                    logging.warning(f"无法获取队列 {queue} 的负载信息")
                    continue

                # 检查是否是高价值队列，如果是，确保不会减少到最小主机数以下
                is_high_value = queue in self.high_value_queues
                min_hosts = self.min_hosts_per_queue.get(queue, 1) if is_high_value else 1

                # 如果当前主机数已经是最小值，且是高价值队列，跳过减少主机的操作
                if is_high_value and len(hosts) <= min_hosts:
                    logging.info(f"高价值队列 {queue} 的主机数 {len(hosts)} 已达最小值 {min_hosts}，跳过减少操作")
                    continue

                for host in hosts:
                    host_load = self.parse_host_load(host)

                    if not host_load:
                        logging.warning(f"无法获取主机 {host} 的负载信息")
                        continue

                    # 构建当前状态向量
                    current_state = [
                        host_load['cpu_usage'],
                        host_load['memory_usage'],
                        queue_load['running_jobs'],
                        queue_load['pending_jobs']
                    ]

                    # 预测需要转移的资源量，传入队列名称以考虑成本和未来作业
                    transfer_amount = self.predict_transfer_amount(current_state, queue)

                    # 根据成本数据和未来作业预测调整转移阈值
                    threshold = self.get_dynamic_threshold(group, queue, future_jobs_by_queue.get(queue, 0))

                    # 记录决策
                    record = {
                        'timestamp': datetime.now(),
                        'queue': queue,
                        'host': host,
                        'cpu_usage': host_load['cpu_usage'],
                        'memory_usage': host_load['memory_usage'],
                        'running_jobs': queue_load['running_jobs'],
                        'pending_jobs': queue_load['pending_jobs'],
                        'transfer_amount': transfer_amount,
                        'cost_saved': 0.0
                    }
                    self.history = pd.concat([self.history, pd.DataFrame([record])], ignore_index=True)

                    # 如果预测需要转移资源且不违反高价值队列的最小主机数限制
                    if transfer_amount > threshold and (not is_high_value or len(hosts) > min_hosts):
                        logging.info(f"主机 {host} 的转移量 {transfer_amount:.2f} 超过阈值 {threshold:.2f}，执行资源转移")
                        if self._transfer_resources(host, transfer_amount):
                            changes_made = True
                    else:
                        logging.info(f"主机 {host} 的转移量 {transfer_amount:.2f} 未超过阈值 {threshold:.2f} 或受高价值队列限制，不执行转移")

            # 动态调整队列限制
            self.adjust_queue_limits()

            # 保存历史数据并训练模型
            self.save_history()
            if changes_made or len(self.history) % 10 == 0:
                self.train_model()

            return changes_made
        except Exception as e:
            logging.error(f"平衡资源时出错: {str(e)}")
            return False

    def calculate_required_hosts(self, future_jobs_by_queue):
        """计算每个队列所需的主机数量，考虑作业优先级、运行时间和主机性能"""
        required_hosts = {}

        for queue, future_jobs in future_jobs_by_queue.items():
            # 获取队列的历史数据
            queue_data = self.job_history[self.job_history['queue'] == queue].copy()

            # 估算每台主机每小时可以处理的作业数
            # 这个值应该根据历史数据调整
            if len(queue_data) > 0:
                # 基于历史数据动态计算处理能力
                avg_run_time = queue_data['avg_run_time'].mean()
                if avg_run_time > 0:
                    # 如果有运行时间数据，使用它来计算
                    jobs_per_host_per_hour = 60 / max(avg_run_time, 10)  # 假设每个主机可以并行运行1个作业
                else:
                    # 否则使用默认值
                    jobs_per_host_per_hour = 2.0
            else:
                jobs_per_host_per_hour = 2.0

            # 考虑队列优先级
            priority = self.queue_priorities.get(queue, 5)
            priority_factor = priority / 10.0  # 将优先级转换为0.1-1.0的因子

            # 计算基础所需主机数
            base_hosts_required = math.ceil(future_jobs / (24 * jobs_per_host_per_hour))

            # 应用优先级调整
            hosts_required = math.ceil(base_hosts_required * (1 + 0.2 * (priority_factor - 0.5)))

            # 确保至少有最小数量的主机
            min_hosts = self.min_hosts_per_queue.get(queue, 1)
            hosts_required = max(hosts_required, min_hosts)

            # 添加预留资源
            if queue in self.high_value_queues:
                # 为高价值队列添加额外预留
                reserve = math.ceil(hosts_required * self.reserve_ratio)
                hosts_required += reserve
                logging.info(f"队列 {queue} 添加 {reserve} 台预留主机（{self.reserve_ratio*100:.0f}%预留率）")

            required_hosts[queue] = hosts_required
            logging.info(f"队列 {queue} 基于未来作业预测需要 {hosts_required} 台主机 (基础需求: {base_hosts_required}, 优先级: {priority})")

        return required_hosts

    def transfer_hosts_to_high_value_queue(self, target_group, target_queue, hosts_needed):
        """将主机转移到高价值队列，考虑主机性能和作业需求"""
        hosts_transferred = 0

        # 按单价从低到高排序其他队列
        other_groups = []
        for group, queue in self.queues.items():
            if group != target_group:
                price = self.queue_prices.get(queue, 5.0)
                priority = self.queue_priorities.get(queue, 5)
                # 计算综合评分：单价越低，优先级越低，越容易被选为源
                score = price * (10 - priority)
                other_groups.append((group, queue, price, priority, score))

        # 按综合评分从低到高排序
        other_groups.sort(key=lambda x: x[4])

        # 从低评分队列开始转移主机
        for source_group, source_queue, price, priority, score in other_groups:
            if hosts_transferred >= hosts_needed:
                break

            # 获取源主机组的主机列表
            source_hosts = self.host_groups.get(source_group, [])

            # 确保源主机组保留最小数量的主机
            min_source_hosts = self.min_hosts_per_queue.get(source_queue, 1)

            # 检查源队列的未来作业预测
            future_jobs = self.predict_future_jobs(source_queue)
            if future_jobs and sum(future_jobs) < 5:
                # 如果预测未来作业很少，可以减少保留的主机数
                min_source_hosts = max(1, min_source_hosts - 1)
                logging.info(f"队列 {source_queue} 预测未来作业很少，减少保留主机数到 {min_source_hosts}")

            available_hosts = max(0, len(source_hosts) - min_source_hosts)

            if available_hosts <= 0:
                continue

            # 选择可以转移的主机数量
            hosts_to_transfer = min(available_hosts, hosts_needed - hosts_transferred)

            # 评估每台主机的适合度
            host_scores = []
            for host in source_hosts:
                # 获取主机负载
                host_load = self.parse_host_load(host)
                if not host_load:
                    continue

                # 获取主机性能数据
                host_capability = self.host_capabilities.get(host, {})
                performance_score = host_capability.get('performance_score', 1.0)

                # 计算负载指标
                load_metric = host_load['cpu_usage'] * 0.5 + host_load['memory_usage'] * 0.3 + host_load['io_usage'] * 0.2

                # 计算综合评分：负载越低，性能越高，越适合转移
                host_score = (1 - load_metric) * performance_score
                host_scores.append((host, host_score))

            # 按评分从高到低排序
            host_scores.sort(key=lambda x: x[1], reverse=True)

            # 转移选定的主机
            for i in range(hosts_to_transfer):
                if i < len(host_scores):
                    host_to_transfer = host_scores[i][0]
                    host_score = host_scores[i][1]

                    # 检查目标队列是否适合这台主机
                    if self._is_host_suitable_for_queue(host_to_transfer, target_queue):
                        logging.info(f"将主机 {host_to_transfer} 从 {source_group} 转移到高价值队列 {target_group} (评分: {host_score:.2f})")

                        if self._transfer_between_host_groups(host_to_transfer, source_group, target_group, 1.0):
                            hosts_transferred += 1
                            logging.info(f"成功将主机转移到高价值队列，已转移 {hosts_transferred}/{hosts_needed}")
                        else:
                            logging.error(f"转移主机到高价值队列失败")
                    else:
                        logging.warning(f"主机 {host_to_transfer} 不适合队列 {target_queue}，跳过")

        return hosts_transferred

    def _is_host_suitable_for_queue(self, host, queue):
        """检查主机是否适合指定队列的作业需求"""
        # 获取主机性能数据
        host_capability = self.host_capabilities.get(host, {})

        # 获取队列的历史作业数据
        queue_data = self.job_history[self.job_history['queue'] == queue].copy()

        # 如果没有足够的数据，假设主机适合
        if len(queue_data) < 10 or not host_capability:
            return True

        # 检查CPU数量是否足够
        avg_run_time = queue_data['avg_run_time'].mean()
        if avg_run_time > 60 and host_capability.get('cpus', 4) < 4:
            # 对于长时间运行的作业，需要更多CPU
            return False

        # 检查内存是否足够
        if queue == 'AL_Ncsim' and host_capability.get('memory', 4096) < 8192:
            # 仿真作业通常需要大量内存
            return False

        # 默认情况下，认为主机适合
        return True

    def get_dynamic_threshold(self, host_group, queue, future_jobs=0):
        """根据成本数据和未来作业预测动态调整转移阈值"""
        base_threshold = 0.3  # 基础阈值

        try:
            # 获取当前周的成本数据
            current_week = datetime.now().isocalendar()[1]
            current_data = self.cost_data[
                (self.cost_data['week'] == current_week) &
                (self.cost_data['host_group'] == host_group) &
                (self.cost_data['queue'] == queue)
            ]

            if not current_data.empty:
                cost = current_data['cost'].iloc[0]
                jobs = current_data['jobs_completed'].iloc[0]

                # 计算每个作业的平均成本
                avg_cost_per_job = cost / (jobs + 1)  # 避免除以零

                # 根据平均成本调整阈值
                if avg_cost_per_job > 10:  # 假设10是高成本的标准
                    # 高成本情况下提高阈值，减少资源转移
                    return base_threshold + 0.1
                elif avg_cost_per_job < 5:  # 假设5是低成本的标准
                    # 低成本情况下降低阈值，增加资源转移
                    return max(0.1, base_threshold - 0.1)

        except Exception as e:
            logging.error(f"计算动态阈值时出错: {str(e)}")

        # 考虑未来作业量
        if future_jobs > 0:
            # 高未来作业量时降低阈值，增加资源
            return max(0.1, base_threshold - 0.1)
        else:
            # 低未来作业量时提高阈值，减少资源
            return min(0.5, base_threshold + 0.1)

    def _transfer_resources(self, host, amount):
        """执行资源转移，包括主机组间的转移"""
        try:
            # 获取主机所属的主机组
            current_host_group = None
            for group, hosts in self.host_groups.items():
                if host in hosts:
                    current_host_group = group
                    break

            if not current_host_group:
                logging.error(f"无法确定主机 {host} 所属的主机组")
                return False

            current_queue = self.queues[current_host_group]

            # 根据转移量计算需要调整的资源
            # 这里假设amount是0-1之间的值，表示需要调整的资源比例
            slots_to_adjust = int(amount * 10)  # 假设每个单位对应10个槽位

            # LSF配置文件路径
            lsf_config_file = '/common/lsftool/lsf/mnt/conf/lsbatch/rdb/configdir/lsb.hosts'

            if slots_to_adjust > 0:
                try:
                    # 读取LSF配置文件
                    with open(lsf_config_file, 'r') as f:
                        lines = f.readlines()

                    # 修改配置文件
                    new_lines = []
                    host_found = False
                    for line in lines:
                        if line.strip().startswith(host):
                            host_found = True
                            # 解析当前行
                            parts = line.strip().split()
                            if len(parts) >= 2:
                                # 修改槽位数
                                current_slots = int(parts[1])
                                new_slots = current_slots + slots_to_adjust
                                parts[1] = str(max(1, new_slots))  # 确保至少有1个槽位
                                new_lines.append(' '.join(parts) + '\n')
                        else:
                            new_lines.append(line)

                    if not host_found:
                        logging.error(f"在LSF配置文件中未找到主机 {host}")
                        return False

                    # 写入修改后的配置
                    with open(lsf_config_file, 'w') as f:
                        f.writelines(new_lines)

                    # 重启LSF服务
                    restart_cmd = 'badmin mbdrestart'
                    logging.info(f"执行命令: {restart_cmd}")
                    result = subprocess.run(restart_cmd, shell=True, capture_output=True, text=True)

                    if result.returncode == 0:
                        logging.info(f"成功调整主机 {host} 的资源并重启LSF服务")

                        # 估算节省的成本
                        cost_saved = slots_to_adjust * 0.5  # 假设每个槽位每小时成本为0.5单位

                        # 更新成本数据
                        self.update_cost_data(current_host_group, current_queue, -cost_saved)  # 负值表示节省

                        # 在历史记录中添加成本节省信息
                        latest_record_index = self.history.index[-1]
                        self.history.at[latest_record_index, 'cost_saved'] = cost_saved

                        return True
                    else:
                        logging.error(f"重启LSF服务失败: {result.stderr}")
                        return False

                except IOError as e:
                    logging.error(f"操作LSF配置文件失败: {str(e)}")
                    return False

                except Exception as e:
                    logging.error(f"修改LSF配置时发生错误: {str(e)}")
                    return False
            else:
                logging.info(f"计算的调整量太小，不执行实际操作")
                return False

        except Exception as e:
            logging.error(f"资源转移过程中出错: {str(e)}")
            return False

    def _find_target_host_group(self, current_host_group, host):
        """寻找适合转移的目标主机组"""
        try:
            # 初始化最佳目标组和最低负载
            best_target_group = None
            lowest_load = float('inf')

            # 遍历所有主机组，寻找负载最低的组
            for group, hosts in self.host_groups.items():
                # 跳过当前主机组
                if group == current_host_group:
                    continue

                # 获取该组对应的队列
                queue = self.queues[group]
                # 获取队列负载
                queue_load = self.get_queue_load(queue)

                if not queue_load:
                    continue

                # 计算综合负载指标（可以根据实际需求调整权重）
                load_metric = queue_load['cpu_usage'] * 0.6 + (queue_load['pending_jobs'] / (queue_load['running_jobs'] + 1)) * 0.4

                # 更新最佳目标组
                if load_metric < lowest_load:
                    lowest_load = load_metric
                    best_target_group = group

            return best_target_group
        except Exception as e:
            logging.error(f"寻找目标主机组时出错: {str(e)}")
            return None

    def _transfer_between_host_groups(self, host, source_group, target_group, transfer_amount):
        """将主机从一个主机组转移到另一个主机组"""
        try:
            logging.info(f"开始将主机 {host} 从 {source_group} 转移到 {target_group}")

            # 使用原生 LSF 命令从源主机组移除主机
            remove_cmd = f"badmin hghostdel {source_group} {host}"
            logging.info(f"执行命令: {remove_cmd}")
            remove_result = subprocess.run(remove_cmd, shell=True, capture_output=True, text=True)

            if remove_result.returncode != 0:
                logging.error(f"从主机组 {source_group} 移除主机 {host} 失败: {remove_result.stderr}")
                return False

            # 使用原生 LSF 命令将主机添加到目标主机组
            add_cmd = f"badmin hghostadd {target_group} {host}"
            logging.info(f"执行命令: {add_cmd}")
            add_result = subprocess.run(add_cmd, shell=True, capture_output=True, text=True)

            if add_result.returncode != 0:
                logging.error(f"将主机 {host} 添加到主机组 {target_group} 失败: {add_result.stderr}")

                # 尝试恢复：将主机添加回源主机组
                recovery_cmd = f"badmin hghostadd {source_group} {host}"
                logging.info(f"尝试恢复，执行命令: {recovery_cmd}")
                subprocess.run(recovery_cmd, shell=True)

                return False

            # 更新内存中的主机组数据
            if host in self.host_groups.get(source_group, []):
                self.host_groups[source_group].remove(host)

            if target_group not in self.host_groups:
                self.host_groups[target_group] = []

            if host not in self.host_groups[target_group]:
                self.host_groups[target_group].append(host)

            # 获取源队列和目标队列
            source_queue = self.queues.get(source_group)
            target_queue = self.queues.get(target_group)

            # 计算成本影响
            if source_queue and target_queue:
                source_price = self.queue_prices.get(source_queue, 5.0)
                target_price = self.queue_prices.get(target_queue, 5.0)

                # 假设转移量对应的成本是单价差异的一定比例
                cost_impact = (target_price - source_price) * transfer_amount * 10

                # 更新成本数据
                self.update_cost_data(source_group, source_queue, cost_impact)

                # 记录转移事件
                self.record_transfer(host, source_group, target_group, "负载转移", transfer_amount, cost_impact)

            logging.info(f"成功将主机 {host} 从 {source_group} 转移到 {target_group}")
            return True

        except Exception as e:
            logging.error(f"转移主机时出错: {str(e)}")
            return False

    def load_job_history(self):
        """加载作业历史数据"""
        if os.path.exists(self.job_history_file):
            self.job_history = pd.read_csv(self.job_history_file)
        else:
            # 创建作业历史数据框架
            self.job_history = pd.DataFrame(columns=[
                'timestamp', 'queue', 'submitted_jobs', 'running_jobs',
                'completed_jobs', 'avg_wait_time', 'avg_run_time', 'avg_job_value'
            ])
            # 初始化默认数据
            default_data = []
            current_time = datetime.now()
            for queue in self.queues.values():
                default_data.append({
                    'timestamp': current_time,
                    'queue': queue,
                    'submitted_jobs': 0,
                    'running_jobs': 0,
                    'completed_jobs': 0,
                    'avg_wait_time': 0,
                    'avg_run_time': 0,
                    'avg_job_value': 0
                })
            self.job_history = pd.DataFrame(default_data)
            self.job_history.to_csv(self.job_history_file, index=False)

    def initialize_job_forecast_model(self):
        """初始化作业预测模型"""
        # 为每个队列创建一个预测模型
        forecast_models = {}
        for queue in self.queues.values():
            # 使用RandomForestRegressor作为基础模型
            forecast_models[queue] = {
                'rf': RandomForestRegressor(
                    n_estimators=200,
                    max_depth=10,
                    min_samples_split=5,
                    random_state=42
                ),
                'seasonal_patterns': {},  # 存储季节性模式
                'last_update': datetime.now()
            }

        # 如果有足够的历史数据，训练模型
        if len(self.job_history) > 24:  # 至少需要24小时的数据
            self.train_job_forecast_models(forecast_models)

        return forecast_models

    def train_job_forecast_models(self, models):
        """训练作业预测模型"""
        for queue, model_data in models.items():
            # 获取该队列的历史数据
            queue_data = self.job_history[self.job_history['queue'] == queue].copy()

            if len(queue_data) < 24:
                logging.info(f"队列 {queue} 的历史数据不足，暂不训练预测模型")
                continue

            try:
                # 添加时间特征
                queue_data['timestamp'] = pd.to_datetime(queue_data['timestamp'])
                queue_data['hour'] = queue_data['timestamp'].dt.hour
                queue_data['day_of_week'] = queue_data['timestamp'].dt.dayofweek
                queue_data['day_of_month'] = queue_data['timestamp'].dt.day
                queue_data['month'] = queue_data['timestamp'].dt.month
                queue_data['is_weekend'] = queue_data['day_of_week'].apply(lambda x: 1 if x >= 5 else 0)
                queue_data['is_night'] = queue_data['hour'].apply(lambda x: 1 if x < 6 or x >= 22 else 0)

                # 创建滞后特征（过去几小时的作业数）
                for lag in [1, 2, 3, 6, 12, 24]:
                    queue_data[f'submitted_lag_{lag}'] = queue_data['submitted_jobs'].shift(lag)

                # 添加移动平均特征
                queue_data['ma_6h'] = queue_data['submitted_jobs'].rolling(window=6).mean()
                queue_data['ma_12h'] = queue_data['submitted_jobs'].rolling(window=12).mean()
                queue_data['ma_24h'] = queue_data['submitted_jobs'].rolling(window=24).mean()

                # 添加趋势特征
                queue_data['trend'] = queue_data['submitted_jobs'].rolling(window=24).mean().diff()

                # 删除包含NaN的行
                queue_data = queue_data.dropna()

                if len(queue_data) < 10:
                    logging.info(f"处理后队列 {queue} 的有效数据不足，暂不训练预测模型")
                    continue

                # 分析季节性模式
                self._analyze_seasonal_patterns(queue_data, model_data)

                # 准备特征和目标变量
                X = queue_data[[
                    'hour', 'day_of_week', 'day_of_month', 'month', 'is_weekend', 'is_night',
                    'submitted_lag_1', 'submitted_lag_2', 'submitted_lag_3',
                    'submitted_lag_6', 'submitted_lag_12', 'submitted_lag_24',
                    'ma_6h', 'ma_12h', 'ma_24h', 'trend'
                ]]
                y = queue_data['submitted_jobs']

                # 训练模型
                model_data['rf'].fit(X, y)
                model_data['last_update'] = datetime.now()

                # 评估模型性能
                try:
                    scores = cross_val_score(model_data['rf'], X, y, cv=min(5, len(X)), scoring='neg_mean_squared_error')
                    rmse = np.sqrt(-scores.mean())
                    logging.info(f"成功训练队列 {queue} 的作业预测模型，RMSE: {rmse:.2f}")
                except Exception as eval_error:
                    logging.warning(f"模型评估失败: {str(eval_error)}，但模型训练已完成")

            except Exception as e:
                logging.error(f"训练队列 {queue} 的作业预测模型失败: {str(e)}")

    def _analyze_seasonal_patterns(self, data, model_data):
        """分析季节性模式"""
        try:
            # 按小时分析
            hourly_pattern = data.groupby('hour')['submitted_jobs'].mean()
            peak_hours = hourly_pattern[hourly_pattern > hourly_pattern.mean()].index.tolist()

            # 按星期几分析
            daily_pattern = data.groupby('day_of_week')['submitted_jobs'].mean()
            peak_days = daily_pattern[daily_pattern > daily_pattern.mean()].index.tolist()

            # 按月份分析
            monthly_pattern = data.groupby('month')['submitted_jobs'].mean()
            peak_months = monthly_pattern[monthly_pattern > monthly_pattern.mean()].index.tolist()

            # 存储季节性模式
            model_data['seasonal_patterns'] = {
                'peak_hours': peak_hours,
                'peak_days': peak_days,
                'peak_months': peak_months,
                'hourly_pattern': hourly_pattern.to_dict(),
                'daily_pattern': daily_pattern.to_dict(),
                'monthly_pattern': monthly_pattern.to_dict()
            }

            logging.info(f"季节性分析: 高峰时段 {peak_hours}, 高峰日 {peak_days}, 高峰月 {peak_months}")

        except Exception as e:
            logging.error(f"分析季节性模式失败: {str(e)}")

    def collect_job_data(self):
        """收集当前作业数据"""
        current_time = datetime.now()

        for queue_name in self.queues.values():
            try:
                # 使用LSF命令获取队列信息
                cmd = f"bqueues -l {queue_name}"
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

                if result.returncode != 0:
                    logging.error(f"获取队列 {queue_name} 信息失败: {result.stderr}")
                    continue

                output = result.stdout

                # 解析输出获取作业信息
                submitted_jobs = 0
                running_jobs = 0
                completed_jobs = 0
                avg_wait_time = 0
                avg_run_time = 0

                # 解析提交的作业数
                submitted_match = re.search(r'NJOBS\s+(\d+)', output)
                if submitted_match:
                    submitted_jobs = int(submitted_match.group(1))

                # 解析运行中的作业数
                running_match = re.search(r'RUN\s+(\d+)', output)
                if running_match:
                    running_jobs = int(running_match.group(1))

                # 获取作业价值（基于队列单价）
                avg_job_value = self.queue_prices.get(queue_name, 5.0)

                # 记录作业数据
                job_data = {
                    'timestamp': current_time,
                    'queue': queue_name,
                    'submitted_jobs': submitted_jobs,
                    'running_jobs': running_jobs,
                    'completed_jobs': completed_jobs,
                    'avg_wait_time': avg_wait_time,
                    'avg_run_time': avg_run_time,
                    'avg_job_value': avg_job_value
                }

                # 添加到历史记录
                self.job_history = pd.concat([self.job_history, pd.DataFrame([job_data])], ignore_index=True)

                logging.info(f"队列 {queue_name} 当前状态: 提交作业 {submitted_jobs}, 运行作业 {running_jobs}")

            except Exception as e:
                logging.error(f"收集队列 {queue_name} 作业数据失败: {str(e)}")

        # 保存作业历史数据
        self.job_history.to_csv(self.job_history_file, index=False)

        # 定期训练预测模型
        if len(self.job_history) % 24 == 0:  # 每24条记录训练一次
            self.train_job_forecast_models(self.job_forecast_model)

    def predict_future_jobs(self, queue_name, hours_ahead=24):
        """预测未来指定小时的作业数量，使用增强的预测模型和季节性分析"""
        if queue_name not in self.job_forecast_model:
            logging.warning(f"队列 {queue_name} 没有预测模型")
            return None

        model_data = self.job_forecast_model[queue_name]

        # 获取该队列的历史数据
        queue_data = self.job_history[self.job_history['queue'] == queue_name].copy()

        if len(queue_data) < 24:
            logging.warning(f"队列 {queue_name} 的历史数据不足，无法预测")
            return None

        try:
            # 获取最近的数据
            queue_data = queue_data.sort_values('timestamp').tail(48)  # 使用更多历史数据

            # 添加时间特征
            queue_data['timestamp'] = pd.to_datetime(queue_data['timestamp'])
            queue_data['hour'] = queue_data['timestamp'].dt.hour
            queue_data['day_of_week'] = queue_data['timestamp'].dt.dayofweek
            queue_data['day_of_month'] = queue_data['timestamp'].dt.day
            queue_data['month'] = queue_data['timestamp'].dt.month
            queue_data['is_weekend'] = queue_data['day_of_week'].apply(lambda x: 1 if x >= 5 else 0)
            queue_data['is_night'] = queue_data['hour'].apply(lambda x: 1 if x < 6 or x >= 22 else 0)

            # 创建滞后特征
            for lag in [1, 2, 3, 6, 12, 24]:
                if lag < len(queue_data):
                    queue_data[f'submitted_lag_{lag}'] = queue_data['submitted_jobs'].shift(lag)
                else:
                    queue_data[f'submitted_lag_{lag}'] = queue_data['submitted_jobs'].mean()

            # 添加移动平均特征
            queue_data['ma_6h'] = queue_data['submitted_jobs'].rolling(window=min(6, len(queue_data))).mean()
            queue_data['ma_12h'] = queue_data['submitted_jobs'].rolling(window=min(12, len(queue_data))).mean()
            queue_data['ma_24h'] = queue_data['submitted_jobs'].rolling(window=min(24, len(queue_data))).mean()

            # 添加趋势特征
            queue_data['trend'] = queue_data['submitted_jobs'].rolling(window=min(24, len(queue_data))).mean().diff()

            # 填充NaN值
            queue_data = queue_data.fillna(method='bfill').fillna(method='ffill')

            # 获取季节性模式
            seasonal_patterns = model_data.get('seasonal_patterns', {})

            # 预测未来每小时的作业数
            future_jobs = []
            last_data = queue_data.iloc[-1].copy()

            current_time = datetime.now()

            for hour in range(hours_ahead):
                # 更新时间特征
                future_time = current_time + timedelta(hours=hour)

                # 准备特征数据
                pred_data = {
                    'timestamp': future_time,
                    'hour': future_time.hour,
                    'day_of_week': future_time.weekday(),
                    'day_of_month': future_time.day,
                    'month': future_time.month,
                    'is_weekend': 1 if future_time.weekday() >= 5 else 0,
                    'is_night': 1 if future_time.hour < 6 or future_time.hour >= 22 else 0
                }

                # 复制滞后特征
                for lag in [1, 2, 3, 6, 12, 24]:
                    pred_data[f'submitted_lag_{lag}'] = last_data[f'submitted_lag_{lag}']

                # 复制移动平均和趋势特征
                pred_data['ma_6h'] = last_data['ma_6h']
                pred_data['ma_12h'] = last_data['ma_12h']
                pred_data['ma_24h'] = last_data['ma_24h']
                pred_data['trend'] = last_data['trend']

                # 转换为DataFrame
                X_pred = pd.DataFrame([pred_data])

                # 选择特征列
                X_pred = X_pred[[
                    'hour', 'day_of_week', 'day_of_month', 'month', 'is_weekend', 'is_night',
                    'submitted_lag_1', 'submitted_lag_2', 'submitted_lag_3',
                    'submitted_lag_6', 'submitted_lag_12', 'submitted_lag_24',
                    'ma_6h', 'ma_12h', 'ma_24h', 'trend'
                ]]

                # 使用模型预测
                try:
                    base_prediction = model_data['rf'].predict(X_pred)[0]
                except Exception as model_error:
                    logging.warning(f"模型预测失败: {str(model_error)}，使用备用方法")
                    # 备用预测方法：使用季节性模式和历史平均值
                    base_prediction = self._fallback_prediction(pred_data, seasonal_patterns, queue_data)

                # 应用季节性调整
                prediction = self._apply_seasonal_adjustment(base_prediction, pred_data, seasonal_patterns)

                # 确保预测值非负
                prediction = max(0, prediction)

                future_jobs.append(prediction)

                # 更新滞后特征
                last_data['submitted_lag_24'] = last_data['submitted_lag_23'] if 'submitted_lag_23' in last_data else last_data['submitted_lag_12']
                last_data['submitted_lag_12'] = last_data['submitted_lag_11'] if 'submitted_lag_11' in last_data else last_data['submitted_lag_6']
                last_data['submitted_lag_6'] = last_data['submitted_lag_5'] if 'submitted_lag_5' in last_data else last_data['submitted_lag_3']
                last_data['submitted_lag_3'] = last_data['submitted_lag_2']
                last_data['submitted_lag_2'] = last_data['submitted_lag_1']
                last_data['submitted_lag_1'] = prediction

                # 更新移动平均
                last_data['ma_6h'] = (last_data['ma_6h'] * 5 + prediction) / 6
                last_data['ma_12h'] = (last_data['ma_12h'] * 11 + prediction) / 12
                last_data['ma_24h'] = (last_data['ma_24h'] * 23 + prediction) / 24

                # 更新趋势
                last_data['trend'] = last_data['ma_24h'] - queue_data['ma_24h'].iloc[-1]

            # 检查预测结果是否合理
            avg_prediction = np.mean(future_jobs)
            avg_historical = queue_data['submitted_jobs'].mean()

            # 如果预测值与历史平均值差异过大，进行调整
            if avg_prediction > avg_historical * 3:
                logging.warning(f"预测值 {avg_prediction:.2f} 远高于历史平均值 {avg_historical:.2f}，进行调整")
                scale_factor = (avg_historical * 2) / avg_prediction
                future_jobs = [job * scale_factor for job in future_jobs]

            return future_jobs

        except Exception as e:
            logging.error(f"预测队列 {queue_name} 的未来作业量失败: {str(e)}")
            return None

    def _fallback_prediction(self, pred_data, seasonal_patterns, historical_data):
        """备用预测方法，基于季节性模式和历史数据"""
        hour = pred_data['hour']
        day_of_week = pred_data['day_of_week']
        month = pred_data['month']

        # 获取历史平均值
        avg_jobs = historical_data['submitted_jobs'].mean()

        # 应用季节性调整
        hourly_pattern = seasonal_patterns.get('hourly_pattern', {})
        daily_pattern = seasonal_patterns.get('daily_pattern', {})
        monthly_pattern = seasonal_patterns.get('monthly_pattern', {})

        hour_factor = hourly_pattern.get(str(hour), 1.0) / avg_jobs if hourly_pattern else 1.0
        day_factor = daily_pattern.get(str(day_of_week), 1.0) / avg_jobs if daily_pattern else 1.0
        month_factor = monthly_pattern.get(str(month), 1.0) / avg_jobs if monthly_pattern else 1.0

        # 计算加权预测值
        prediction = avg_jobs * (hour_factor * 0.5 + day_factor * 0.3 + month_factor * 0.2)

        return prediction

    def _apply_seasonal_adjustment(self, base_prediction, pred_data, seasonal_patterns):
        """应用季节性调整到基础预测值"""
        hour = pred_data['hour']
        day_of_week = pred_data['day_of_week']

        # 检查是否是高峰时段
        peak_hours = seasonal_patterns.get('peak_hours', [])
        peak_days = seasonal_patterns.get('peak_days', [])

        # 应用高峰时段调整
        if hour in peak_hours:
            base_prediction *= 1.2  # 高峰时段增加20%

        # 应用高峰日调整
        if day_of_week in peak_days:
            base_prediction *= 1.15  # 高峰日增加15%

        # 应用周末调整
        if pred_data['is_weekend']:
            base_prediction *= 0.7  # 周末减少30%

        # 应用夜间调整
        if pred_data['is_night']:
            base_prediction *= 0.8  # 夜间减少20%

        return base_prediction

    def list_host_group_members(self, group):
        """列出主机组成员"""
        try:
            cmd = f"badmin hghostinfo {group}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode != 0:
                logging.error(f"获取主机组 {group} 信息失败: {result.stderr}")
                return []

            output = result.stdout

            # 解析输出，提取主机列表
            hosts = []
            for line in output.splitlines():
                if ":" not in line:
                    continue

                parts = line.split(":", 1)
                if parts[0].strip() == "HOSTS":
                    hosts_str = parts[1].strip()
                    hosts = [h.strip() for h in hosts_str.split() if h.strip()]
                    break

            return hosts

        except Exception as e:
            logging.error(f"列出主机组成员时出错: {str(e)}")
            return []

    def adjust_queue_limits(self):
        """动态调整队列的 MAX 和 JL/U 参数，使用机器学习优化器"""
        try:
            for queue_name in self.queues.values():
                # 获取队列当前状态
                queue_load = self.get_queue_load(queue_name)
                if not queue_load:
                    continue

                # 获取当前 MAX 和 JL/U 值
                current_max = queue_load.get('max_jobs', 0)
                current_jlu = queue_load.get('max_jobs_per_user', 0)

                # 准备当前队列数据
                queue_data = pd.DataFrame([{
                    'max_jobs': current_max,
                    'max_jobs_per_user': current_jlu,
                    'running_jobs': queue_load.get('running_jobs', 0),
                    'pending_jobs': queue_load.get('pending_jobs', 0),
                    'pjobs': queue_load.get('pjobs', 0),
                    'cpu_usage': queue_load.get('cpu_usage', 0),
                    'memory_usage': queue_load.get('memory_usage', 0),
                    'wait_time': queue_load.get('wait_time', 0)
                }])

                # 使用机器学习模型预测最优值
                predicted_max, predicted_jlu = self.limit_optimizer.predict_limits(queue_data)

                if predicted_max is not None and predicted_jlu is not None:
                    # 计算调整幅度
                    max_change = abs(predicted_max - current_max) / current_max if current_max > 0 else 1.0
                    jlu_change = abs(predicted_jlu - current_jlu) / current_jlu if current_jlu > 0 else 1.0

                    # 只有当变化超过阈值时才进行调整
                    if max_change > 0.1:  # 10%的变化阈值
                        logging.info(f"队列 {queue_name} 的MAX值从 {current_max} 调整为 {predicted_max}")
                        self._adjust_queue_max(queue_name, predicted_max)

                    if jlu_change > 0.1:  # 10%的变化阈值
                        logging.info(f"队列 {queue_name} 的JL/U值从 {current_jlu} 调整为 {predicted_jlu}")
                        self._adjust_queue_jlu(queue_name, predicted_jlu)

                # 更新历史数据
                self.limit_optimizer.update_history(queue_name, queue_data)

        except Exception as e:
            logging.error(f"调整队列限制时出错: {str(e)}")

    def _adjust_queue_max(self, queue_name, new_max):
        """调整队列的 MAX 参数"""
        try:
            cmd = f"badmin qedit {queue_name} MAX {new_max}"
            logging.info(f"执行命令: {cmd}")
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                logging.info(f"成功将队列 {queue_name} 的 MAX 调整为 {new_max}")
                return True
            else:
                logging.error(f"调整队列 {queue_name} 的 MAX 失败: {result.stderr}")
                return False
        except Exception as e:
            logging.error(f"调整队列 MAX 时出错: {str(e)}")
            return False

    def _adjust_queue_jlu(self, queue_name, new_jlu):
        """调整队列的 JL/U 参数"""
        try:
            cmd = f"badmin qedit {queue_name} JL/U {new_jlu}"
            logging.info(f"执行命令: {cmd}")
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                logging.info(f"成功将队列 {queue_name} 的 JL/U 调整为 {new_jlu}")
                return True
            else:
                logging.error(f"调整队列 {queue_name} 的 JL/U 失败: {result.stderr}")
                return False
        except Exception as e:
            logging.error(f"调整队列 JL/U 时出错: {str(e)}")
            return False

def generate_cost_report(balancer):
    """生成成本报告"""
    try:
        if not hasattr(balancer, 'cost_data') or balancer.cost_data.empty:
            logging.warning("没有足够的成本数据生成报告")
            return

        # 按周汇总成本数据
        weekly_summary = balancer.cost_data.groupby(['week', 'host_group', 'queue']).agg({
            'cost': 'sum',
            'jobs_completed': 'sum'
        }).reset_index()

        # 计算每个作业的平均成本
        weekly_summary['avg_cost_per_job'] = weekly_summary['cost'] / weekly_summary['jobs_completed'].replace(0, 1)

        # 保存报告到CSV文件
        report_file = f'cost_report_{datetime.now().strftime("%Y%m%d")}.csv'
        weekly_summary.to_csv(report_file, index=False)
        logging.info(f"成本报告已生成: {report_file}")

        # 打印报告摘要
        current_week = datetime.now().isocalendar()[1]
        current_week_data = weekly_summary[weekly_summary['week'] == current_week]

        if not current_week_data.empty:
            total_cost = current_week_data['cost'].sum()
            total_jobs = current_week_data['jobs_completed'].sum()
            logging.info(f"本周总成本: {total_cost:.2f}, 总作业数: {total_jobs}")

            # 按主机组打印详细信息
            for _, row in current_week_data.iterrows():
                logging.info(f"主机组: {row['host_group']}, 队列: {row['queue']}, "
                           f"成本: {row['cost']:.2f}, 作业数: {row['jobs_completed']}, "
                           f"平均成本: {row['avg_cost_per_job']:.2f}")

        # 生成主机组转移报告
        generate_transfer_report(balancer)
    except Exception as e:
        logging.error(f"生成成本报告时出错: {str(e)}")

def generate_transfer_report(balancer):
    """生成主机组转移报告（成本单位：人民币/元）"""
    if not hasattr(balancer, 'transfer_history') or len(balancer.transfer_history) < 1:
        logging.warning("没有主机组转移历史记录")
        return

    try:
        logging.info("===== 主机组转移报告 =====")

        # 按源主机组和目标主机组统计转移次数
        transfer_stats = balancer.transfer_history.groupby(['source_group', 'target_group']).size().reset_index(name='transfer_count')

        # 计算每个转移方向的总成本节省
        cost_savings = balancer.transfer_history.groupby(['source_group', 'target_group'])['cost_saved'].sum().reset_index()

        # 合并统计结果
        transfer_stats = pd.merge(transfer_stats, cost_savings, on=['source_group', 'target_group'])

        # 打印转移统计
        logging.info("主机组转移统计:")
        for _, row in transfer_stats.iterrows():
            logging.info(f"从 {row['source_group']} 到 {row['target_group']}:")
            logging.info(f"  转移次数: {row['transfer_count']}")
            logging.info(f"  总成本节省: {row['cost_saved']:.2f} 元")
            logging.info(f"  平均每次转移节省: {row['cost_saved']/row['transfer_count']:.2f} 元")

        # 打印最近的转移记录
        recent_transfers = balancer.transfer_history.sort_values('timestamp', ascending=False).head(5)
        logging.info("最近的转移记录:")
        for _, row in recent_transfers.iterrows():
            timestamp = pd.to_datetime(row['timestamp'])
            logging.info(f"{timestamp.strftime('%Y-%m-%d %H:%M:%S')} - 主机 {row['host']} 从 {row['source_group']} 转移到 {row['target_group']}")
            logging.info(f"  原因: {row['reason']}")
            logging.info(f"  成本节省: {row['cost_saved']:.2f} 元")

        # 计算主机组转移的整体效果
        total_savings = balancer.transfer_history['cost_saved'].sum()
        total_transfers = len(balancer.transfer_history)
        logging.info(f"总计: {total_transfers} 次主机组转移，节省成本 {total_savings:.2f} 元")

    except Exception as e:
        logging.error(f"生成主机组转移报告时出错: {str(e)}")

def main():
    import argparse

    # 检查依赖
    if not check_dependencies():
        sys.exit(1)

    # 检查LSF环境
    if not check_lsf_environment():
        logging.warning("LSF环境可能未正确配置，继续运行可能导致错误")

    # 解析命令行参数
    parser = argparse.ArgumentParser(description='LSF资源自动平衡器')
    parser.add_argument('--interval', type=int, default=300, help='检查间隔(秒)')
    parser.add_argument('--threshold', type=float, default=0.3, help='资源转移阈值')
    parser.add_argument('--report', action='store_true', help='生成成本报告')
    parser.add_argument('--debug', action='store_true', help='启用调试日志')
    parser.add_argument('--transfer-report', action='store_true', help='生成主机组转移报告')
    parser.add_argument('--transfer-host', type=str, help='手动将指定主机转移到另一个主机组')
    parser.add_argument('--target-group', type=str, help='手动转移时的目标主机组')
    parser.add_argument('--cost-weight', type=float, default=0.4, help='成本因素的权重(0-1)')
    parser.add_argument('--list-group', type=str, help='列出指定主机组的成员，使用原生LSF命令')
    args = parser.parse_args()

    # 设置日志级别
    if args.debug:
        logging.getLogger().setLevel(logging.DEBUG)
        logging.info("已启用调试模式")

    balancer = LSFBalancer()

    # 设置成本权重
    if args.cost_weight is not None:
        balancer.set_cost_weight(args.cost_weight)

    # 如果只需要生成报告
    if args.report:
        generate_cost_report(balancer)
        return

    if args.transfer_report:
        generate_transfer_report(balancer)
        return

    # 处理手动转移主机
    if args.transfer_host and args.target_group:
        host_to_transfer = args.transfer_host
        target_group = args.target_group

        # 找到主机当前所在的主机组
        current_group = None
        for group, hosts in balancer.host_groups.items():
            if host_to_transfer in hosts:
                current_group = group
                break

        if not current_group:
            logging.error(f"找不到主机 {host_to_transfer} 所在的主机组")
            sys.exit(1)

        if current_group == target_group:
            logging.warning(f"主机 {host_to_transfer} 已经在目标主机组 {target_group} 中")
            sys.exit(0)

        logging.info(f"手动将主机 {host_to_transfer} 从 {current_group} 转移到 {target_group}")

        if balancer._transfer_between_host_groups(host_to_transfer, current_group, target_group, 1.0):
            logging.info(f"手动转移成功")
            sys.exit(0)
        else:
            logging.error(f"手动转移失败")

    # 处理列出主机组成员
    if args.list_group:
        cmd = f"badmin hghostinfo {args.list_group}"
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"主机组 {args.list_group} 信息:")
            print(result.stdout)
        else:
            print(f"获取主机组 {args.list_group} 信息失败: {result.stderr}")
        sys.exit(0)

    # 记录启动信息
    logging.info(f"LSF资源自动平衡器已启动，检查间隔: {args.interval}秒，转移阈值: {args.threshold}")

    # 记录上次生成报告和保存模型的时间
    last_report_time = datetime.now()
    last_model_save_time = datetime.now()

    try:
        while True:
            try:
                # 执行资源平衡
                changes_made = balancer.balance_resources()

                # 每天生成一次成本报告
                current_time = datetime.now()
                if (current_time - last_report_time).days >= 1:
                    generate_cost_report(balancer)
                    last_report_time = current_time

                # 每小时保存一次模型状态
                if (current_time - last_model_save_time).seconds >= 3600:
                    if hasattr(balancer, 'model') and balancer.model is not None:
                        joblib.dump(balancer.model, f'lsf_model_backup_{current_time.strftime("%Y%m%d_%H%M")}.joblib')
                        logging.info("已创建模型备份")
                    last_model_save_time = current_time

                # 根据是否有变化调整休眠时间
                if changes_made:
                    time.sleep(args.interval // 2)  # 如果有变化，缩短检查间隔
                else:
                    time.sleep(args.interval)

            except Exception as e:
                logging.error(f"运行出错: {str(e)}")
                time.sleep(60)  # 出错后等待1分钟再继续
    except KeyboardInterrupt:
        # 优雅地处理Ctrl+C
        logging.info("接收到中断信号，正在保存状态并退出...")
        balancer.save_history()
        generate_cost_report(balancer)
        logging.info("LSF资源自动平衡器已停止")

if __name__ == "__main__":
    main()
